/**
 * <PERSON><PERSON> <PERSON>ụ sử dụng hàm createPermissionGuard
 * 
 * Hàm createPermissionGuard giú<PERSON> tái sử dụng logic kiểm tra quyền trong route guards
 * thay vì phải viết lại logic tương tự nhiều lần.
 */

import { createPermissionGuard } from './permission.helper.js'

// ===== CÁCH SỬ DỤNG CŨ (trước khi có helper) =====
const oldWayExample = {
  path: '/dashboard',
  name: 'dashboard',
  component: () => import('@/views/Dashboard.vue'),
  meta: {
    title: 'Dashboard',
    authRequired: true,
    permissions: ['dashboard.view'],
    beforeResolve(routeTo, routeFrom, next) {
      // Logic này được lặp lại nhiều lần trong các route khác nhau
      const { useAuthStore } = require('@/state/index.js')
      const { storeToRefs } = require('pinia')
      const { hasPermission, getUserPermissions } = require('@/utils/helpers/permission.helper.js')
      
      const authStore = useAuthStore()
      const { authUser } = storeToRefs(authStore)
      const userPermissions = getUserPermissions(authUser?.value)
      
      if (!hasPermission(['dashboard.view'], userPermissions)) {
        next({ name: 'forbidden' })
      } else {
        next()
      }
    },
  },
}

// ===== CÁCH SỬ DỤNG MỚI (với helper) =====
const newWayExample = {
  path: '/dashboard',
  name: 'dashboard',
  component: () => import('@/views/Dashboard.vue'),
  meta: {
    title: 'Dashboard',
    authRequired: true,
    permissions: ['dashboard.view'],
    // Chỉ cần 1 dòng thay vì cả đoạn code phía trên!
    beforeResolve: createPermissionGuard(['dashboard.view']),
  },
}

// ===== CÁC VÍ DỤ KHÁC =====

// Route cần nhiều quyền (OR logic - chỉ cần 1 trong các quyền)
const multiplePermissionsExample = {
  path: '/admin',
  name: 'admin',
  component: () => import('@/views/Admin.vue'),
  meta: {
    title: 'Admin Panel',
    authRequired: true,
    permissions: ['admin.view', 'super_admin.view'],
    beforeResolve: createPermissionGuard(['admin.view', 'super_admin.view']),
  },
}

// Route chỉ cần 1 quyền cụ thể
const singlePermissionExample = {
  path: '/users',
  name: 'users',
  component: () => import('@/views/Users.vue'),
  meta: {
    title: 'User Management',
    authRequired: true,
    permissions: ['users.view'],
    beforeResolve: createPermissionGuard(['users.view']),
  },
}

// Route cho các action cụ thể
const specificActionExample = {
  path: '/users/create',
  name: 'users-create',
  component: () => import('@/views/Users/<USER>'),
  meta: {
    title: 'Create User',
    authRequired: true,
    permissions: ['users.create'],
    beforeResolve: createPermissionGuard(['users.create']),
  },
}

// ===== LỢI ÍCH CỦA HELPER =====
/**
 * 1. Giảm code duplication: Không cần viết lại logic kiểm tra quyền
 * 2. Dễ maintain: Chỉ cần sửa 1 chỗ trong helper thay vì sửa nhiều route
 * 3. Consistent: Logic kiểm tra quyền giống nhau ở tất cả route
 * 4. Clean code: Route config ngắn gọn và dễ đọc hơn
 * 5. Reusable: Có thể sử dụng cho bất kỳ route nào cần kiểm tra quyền
 */

export {
  oldWayExample,
  newWayExample,
  multiplePermissionsExample,
  singlePermissionExample,
  specificActionExample,
}
